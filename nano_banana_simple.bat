@echo off
setlocal enabledelayedexpansion

echo nano_banana compression tool
echo =============================

REM Configuration
set prefix=nano_banana
set source=.

REM Find 7-Zip
if exist "C:\PROGRA~1\7-Zip\7z.exe" (
    set zippath=C:\PROGRA~1\7-Zip\7z.exe
    echo Found 7-Zip
) else (
    echo ERROR: 7-Zip not found!
    echo Please install 7-Zip from https://www.7-zip.org/
    pause
    exit /b 1
)

REM Get date MMDD
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set dt=%%a
set date_str=!dt:~4,2!!dt:~6,2!

REM Find next number
set max_num=0
for /f "delims=" %%f in ('dir /b "%prefix%!date_str!_*.zip" 2^>nul') do (
    set "filename=%%f"
    for /f "tokens=3 delims=_." %%n in ("!filename!") do (
        if %%n gtr !max_num! set max_num=%%n
    )
)
set /a new_num=max_num + 1

REM Create zip file
set zip_file=%prefix%!date_str!_!new_num!.zip
echo Creating: !zip_file!

"!zippath!" a -tzip "!zip_file!" "%source%\*"

if !errorlevel! equ 0 (
    echo Success! Created: !zip_file!
) else (
    echo Failed with error: !errorlevel!
)

pause
