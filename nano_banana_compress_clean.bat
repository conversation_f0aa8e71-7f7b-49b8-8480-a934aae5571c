@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul 2>&1

:: ==============================
:: nano_banana_compress.bat
:: Clean English version (UTF-8)
:: ==============================

:: Configuration
set "prefix=nano_banana"   :: Prefix of zip file name
set "source=."             :: Source folder: current directory
set "zippath=C:\PROGRA~1\7-Zip\7z.exe"  :: Path to 7-Zip executable (using short path)

:: Check if 7-Zip exists
if not exist "%zippath%" (
    echo ERROR: 7-Zip not found!
    echo 1. Check if 7-Zip is installed (default path: C:\Program Files\7-Zip)
    echo 2. Or modify the "zippath" variable in this script
    pause
    exit /b 1
)

:: Check if source folder exists
if not exist "%source%" (
    echo ERROR: Source folder not found!
    pause
    exit /b 1
)

:: Get date string (MMDD)
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "date_str=!dt:~4,2!!dt:~6,2!"

:: Find latest index number
set "max_num=0"
for /f "tokens=2 delims=_." %%a in ('dir /b "%prefix%!date_str!_*.zip" 2^>nul') do (
    if %%a gtr !max_num! set "max_num=%%a"
)
set /a "new_num=max_num + 1"

:: Generate zip file name
set "zip_file=%prefix%!date_str!_!new_num!.zip"
echo Compressing... Output file: !zip_file!
"%zippath%" a -tzip "!zip_file!" "%source%\*"

:: Check result
if %errorlevel% equ 0 (
    echo Compression successful! File created: !zip_file!
) else (
    echo Compression failed. Please check if 7-Zip and source files exist.
)

pause
